package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AnswerSheetDTO;
import com.yzedulife.service.entity.AnswerSheet;
import com.yzedulife.service.entity.AnswerDetail;
import com.yzedulife.service.mapper.AnswerSheetMapper;
import com.yzedulife.service.mapper.AnswerDetailMapper;
import com.yzedulife.service.service.AnswerSheetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 答卷服务实现类
 */
@Service
public class AnswerSheetServiceImpl implements AnswerSheetService {

    @Autowired
    private AnswerSheetMapper answerSheetMapper;
    
    @Autowired
    private AnswerDetailMapper answerDetailMapper;

    @Override
    public AnswerSheetDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        AnswerSheet answerSheet = answerSheetMapper.selectById(id);
        if (answerSheet == null) {
            throw new BusinessException("答卷不存在");
        }
        return answerSheet.toDTO();
    }

    @Override
    public AnswerSheetDTO create(AnswerSheetDTO answerSheetDTO) throws BusinessException {
        if (answerSheetDTO == null) {
            throw new BusinessException("答卷信息不能为空");
        }
        if (answerSheetDTO.getQuestionnaireId() == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (!StringUtils.hasText(answerSheetDTO.getSubmitterType())) {
            throw new BusinessException("提交者类型不能为空");
        }
        
        // 验证提交者类型和对应的用户ID
        if ("STUDENT".equals(answerSheetDTO.getSubmitterType())) {
            if (answerSheetDTO.getStudentUserId() == null) {
                throw new BusinessException("学生用户ID不能为空");
            }
            // 检查学生是否已经答过该问卷
            if (hasUserAnswered(answerSheetDTO.getQuestionnaireId(), "STUDENT", answerSheetDTO.getStudentUserId())) {
                throw new BusinessException("该学生已经答过此问卷");
            }
        } else if ("OTHER".equals(answerSheetDTO.getSubmitterType())) {
            if (answerSheetDTO.getOtherUserId() == null) {
                throw new BusinessException("社会人士用户ID不能为空");
            }
            // 检查社会人士是否已经答过该问卷
            if (hasUserAnswered(answerSheetDTO.getQuestionnaireId(), "OTHER", answerSheetDTO.getOtherUserId())) {
                throw new BusinessException("该用户已经答过此问卷");
            }
        } else {
            throw new BusinessException("无效的提交者类型");
        }
        
        AnswerSheet answerSheet = answerSheetDTO.toEntity();
        answerSheet.setId(null); // 确保是新增
        if (answerSheet.getSubmitTime() == null) {
            answerSheet.setSubmitTime(LocalDateTime.now());
        }
        
        int result = answerSheetMapper.insert(answerSheet);
        if (result <= 0) {
            throw new BusinessException("创建答卷失败");
        }
        return answerSheet.toDTO();
    }

    @Override
    public AnswerSheetDTO update(AnswerSheetDTO answerSheetDTO) throws BusinessException {
        if (answerSheetDTO == null || answerSheetDTO.getId() == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        
        // 检查答卷是否存在
        AnswerSheet existingAnswerSheet = answerSheetMapper.selectById(answerSheetDTO.getId());
        if (existingAnswerSheet == null) {
            throw new BusinessException("答卷不存在");
        }
        
        AnswerSheet answerSheet = answerSheetDTO.toEntity();
        int result = answerSheetMapper.updateById(answerSheet);
        if (result <= 0) {
            throw new BusinessException("更新答卷失败");
        }
        return answerSheetMapper.selectById(answerSheet.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        
        AnswerSheet answerSheet = answerSheetMapper.selectById(id);
        if (answerSheet == null) {
            throw new BusinessException("答卷不存在");
        }
        
        // 删除答卷相关的答案详情
        answerDetailMapper.delete(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, id));
        
        int result = answerSheetMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<AnswerSheetDTO> getByQuestionnaireId(Long questionnaireId) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        List<AnswerSheet> answerSheets = answerSheetMapper.selectList(new LambdaQueryWrapper<AnswerSheet>()
                .eq(AnswerSheet::getQuestionnaireId, questionnaireId)
                .orderByDesc(AnswerSheet::getSubmitTime));
        return answerSheets.stream().map(AnswerSheet::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<AnswerSheetDTO> getByStudentUserId(Long studentUserId) throws BusinessException {
        if (studentUserId == null) {
            throw new BusinessException("学生用户ID不能为空");
        }
        List<AnswerSheet> answerSheets = answerSheetMapper.selectList(new LambdaQueryWrapper<AnswerSheet>()
                .eq(AnswerSheet::getStudentUserId, studentUserId)
                .orderByDesc(AnswerSheet::getSubmitTime));
        return answerSheets.stream().map(AnswerSheet::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<AnswerSheetDTO> getByOtherUserId(Long otherUserId) throws BusinessException {
        if (otherUserId == null) {
            throw new BusinessException("社会人士用户ID不能为空");
        }
        List<AnswerSheet> answerSheets = answerSheetMapper.selectList(new LambdaQueryWrapper<AnswerSheet>()
                .eq(AnswerSheet::getOtherUserId, otherUserId)
                .orderByDesc(AnswerSheet::getSubmitTime));
        return answerSheets.stream().map(AnswerSheet::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean submitAnswerSheet(Long answerSheetId) throws BusinessException {
        if (answerSheetId == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        
        AnswerSheet answerSheet = answerSheetMapper.selectById(answerSheetId);
        if (answerSheet == null) {
            throw new BusinessException("答卷不存在");
        }
        
        // 更新提交时间
        answerSheet.setSubmitTime(LocalDateTime.now());
        int result = answerSheetMapper.updateById(answerSheet);
        return result > 0;
    }

    @Override
    public Integer getCountByQuestionnaireId(Long questionnaireId) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        return Math.toIntExact(answerSheetMapper.selectCount(new LambdaQueryWrapper<AnswerSheet>()
                .eq(AnswerSheet::getQuestionnaireId, questionnaireId)));
    }

    @Override
    public Boolean hasUserAnswered(Long questionnaireId, String submitterType, Long userId) throws BusinessException {
        if (questionnaireId == null || !StringUtils.hasText(submitterType) || userId == null) {
            return false;
        }
        
        LambdaQueryWrapper<AnswerSheet> wrapper = new LambdaQueryWrapper<AnswerSheet>()
                .eq(AnswerSheet::getQuestionnaireId, questionnaireId)
                .eq(AnswerSheet::getSubmitterType, submitterType);
        
        if ("STUDENT".equals(submitterType)) {
            wrapper.eq(AnswerSheet::getStudentUserId, userId);
        } else if ("OTHER".equals(submitterType)) {
            wrapper.eq(AnswerSheet::getOtherUserId, userId);
        } else {
            return false;
        }
        
        return answerSheetMapper.exists(wrapper);
    }

    @Override
    public List<AnswerSheetDTO> getAll() throws BusinessException {
        List<AnswerSheet> answerSheets = answerSheetMapper.selectList(new LambdaQueryWrapper<AnswerSheet>()
                .orderByDesc(AnswerSheet::getSubmitTime));
        return answerSheets.stream().map(AnswerSheet::toDTO).collect(java.util.stream.Collectors.toList());
    }
}
